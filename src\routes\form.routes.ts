import { Hono } from "hono";
import {createForm,updateForm,getUserForms,publishForm,searchForms,getPublicForm,getForm,cloneForm,deleteForm, createFormUsingTemplate, createFormWithAI, copyFormToWorkspace} from "../controllers/form.controllers"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const form = new Hono();
form.post("/createform",verifySupabaseAuth,createForm);
form.put("/updateform/:id",verifySupabaseAuth,updateForm);
form.get("/",verifySupabaseAuth,getUserForms);
form.put("/publish/:id",verifySupabaseAuth,publishForm);
form.get("/search",verifySupabaseAuth, searchForms);
form.get("/public/:id",getPublicForm);
form.get("/:id",verifySupabaseAuth,getForm);
form.get("clone/:id",verifySupabaseAuth,cloneForm);
form.delete("/:id", verifySupaba<PERSON>A<PERSON>, deleteForm);
form.post("/createform/template",verifySupabaseAuth,createFormUsingTemplate);
form.post("/create-with-ai", verifySupabaseAuth, createFormWithAI);
form.get("/copy-to-workspace/:formId", verifySupabaseAuth, copyFormToWorkspace);
export {form};
