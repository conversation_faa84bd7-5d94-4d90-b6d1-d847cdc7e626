import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Share a form with a user
const shareForm = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { form_id, user_id, access_type } = body;

    if (!form_id || !user_id || !access_type) {
      return sendApiError(c, "Form ID, user ID, and access type are required", 400);
    }

    // Validate access type
    if (!['view', 'edit', 'owner', 'copy'].includes(access_type)) {
      return sendApiError(c, "Invalid access type. Must be 'view', 'edit', 'owner', or 'copy'", 400);
    }

    // Check if the current user is the owner of the form
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("created_by")
      .eq("id", form_id)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      // Check if the user has owner access through form_user_access
      const { data: ownerAccess, error: accessError } = await supabase
        .from("automate_form_user_access")
        .select("id")
        .eq("form_id", form_id)
        .eq("user_id", user.user.id)
        .eq("access_type", "owner")
        .single();

      if (accessError || !ownerAccess) {
        return sendApiError(c, "You don't have permission to share this form", 403);
      }
    }

    // Check if the user to share with exists
    const { data: targetUser, error: userError } = await supabase
      .from("user_profile")
      .select("id")
      .eq("id", user_id)
      .single();

    if (userError || !targetUser) {
      return sendApiError(c, "User not found", 404);
    }

    // Create or update the form access
    const { data, error } = await supabase
      .from("automate_form_user_access")
      .upsert({
        form_id,
        user_id,
        access_type,
        granted_at: new Date().toISOString()
      }, {
        onConflict: 'form_id,user_id'
      })
      .select();

    if (error) {
      console.error("Share Form Error:", error);
      return sendApiError(c, "Failed to share form", 500);
    }

    return sendApiResponse(c, { 
      message: "Form shared successfully", 
      access: data 
    }, 200);
  } catch (err) {
    console.error("Share Form Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get forms shared with the current user
const getSharedForms = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const limit = parseInt(c.req.query("limit") || "10", 10);
    const offset = parseInt(c.req.query("offset") || "0", 10);

    // Get forms shared with the user
    const { data, error, count } = await supabase
      .from("automate_form_user_access")
      .select(`
        id,
        access_type,
        granted_at,
        automate_forms:form_id (
          id,
          title,
          description,
          type,
          heading,
          published,
          created_at,
          updated_at,
          created_by,
          user_profile:created_by (
            first_name,
            last_name
          )
        )
      `, { count: 'exact' })
      .eq("user_id", user.user.id)
      .order('granted_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error("Get Shared Forms Error:", error);
      return sendApiError(c, "Failed to retrieve shared forms", 500);
    }

    return sendApiResponse(c, { 
      forms: data,
      count: count || 0,
      limit,
      offset
    }, 200);
  } catch (err) {
    console.error("Get Shared Forms Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Remove form access for a user
const removeFormAccess = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const accessId = c.req.param("id");
    if (!accessId) {
      return sendApiError(c, "Access ID is required", 400);
    }

    // Get the access record to check permissions
    const { data: access, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("form_id, user_id")
      .eq("id", accessId)
      .single();

    if (accessError || !access) {
      return sendApiError(c, "Access record not found", 404);
    }

    // Check if the current user is the owner of the form
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("created_by")
      .eq("id", access.form_id)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      // Check if the user has owner access
      const { data: ownerAccess, error: ownerError } = await supabase
        .from("automate_form_user_access")
        .select("id")
        .eq("form_id", access.form_id)
        .eq("user_id", user.user.id)
        .eq("access_type", "owner")
        .single();

      if (ownerError || !ownerAccess) {
        return sendApiError(c, "You don't have permission to remove this access", 403);
      }
    }

    // Delete the access record
    const { error: deleteError } = await supabase
      .from("automate_form_user_access")
      .delete()
      .eq("id", accessId);

    if (deleteError) {
      console.error("Remove Form Access Error:", deleteError);
      return sendApiError(c, "Failed to remove form access", 500);
    }

    return sendApiResponse(c, { 
      message: "Form access removed successfully" 
    }, 200);
  } catch (err) {
    console.error("Remove Form Access Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get users with access to a form
const getFormAccessUsers = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("form_id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Check if the user has access to the form
    const { data: form, error: formError } = await supabase
      .from("forms")
      .select("created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    const isOwner = form.created_by === user.user.id;
    if (!isOwner) {
      // Check if the user has at least view access
      const { data: userAccess, error: accessError } = await supabase
        .from("form_user_access")
        .select("access_type")
        .eq("form_id", formId)
        .eq("user_id", user.user.id)
        .single();

      if (accessError || !userAccess) {
        return sendApiError(c, "You don't have access to this form", 403);
      }
    }

    // Get all users with access to the form
    const { data, error } = await supabase
      .from("form_user_access")
      .select(`
        id,
        access_type,
        granted_at,
        user:user_id (
          id,
          first_name,
          last_name,
          email,
          profile_image
        )
      `)
      .eq("form_id", formId)
      .order('granted_at', { ascending: false });

    if (error) {
      console.error("Get Form Access Users Error:", error);
      return sendApiError(c, "Failed to retrieve form access users", 500);
    }

    return sendApiResponse(c, { 
      users: data,
    }, 200);
  } catch (err) {
    console.error("Get Form Access Users Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { shareForm, getSharedForms, removeFormAccess, getFormAccessUsers };