import { supabase } from "../db";

export interface FormPermissionResult {
  hasPermission: boolean;
  form?: any;
  settings?: any;
}

/**
 * Check if user has permission to perform an action on a form
 * @param userId - The user ID
 * @param formId - The form ID
 * @param requiredAccess - Array of required access types ['view', 'edit', 'owner']
 * @returns Promise<FormPermissionResult>
 */
export const checkFormPermission = async (
  userId: string,
  formId: string,
  requiredAccess: string[]
): Promise<FormPermissionResult> => {
  try {
    // Get form details with settings
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(`
        id, created_by, workspace_id,
        automate_form_settings(
          is_public, accept_responses, copy_allowed,
          thank_you_type, thank_you_data, thank_you_url
        )
      `)
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return { hasPermission: false };
    }

    const settings = form.automate_form_settings?.[0];

    // Check if user is the original creator (always has all permissions)
    if (form.created_by === userId) {
      return { hasPermission: true, form, settings };
    }

    // Check if user has required access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .in("access_type", requiredAccess)
      .single();

    if (!accessError && userAccess) {
      return { hasPermission: true, form, settings };
    }

    return { hasPermission: false, form, settings };
  } catch (err) {
    console.error("Error checking form permission:", err);
    return { hasPermission: false };
  }
};

/**
 * Check if user has permission to view a form (including public forms)
 * @param userId - The user ID (can be null for public access)
 * @param formId - The form ID
 * @returns Promise<FormPermissionResult>
 */
export const checkFormViewPermission = async (
  userId: string | null,
  formId: string
): Promise<FormPermissionResult> => {
  try {
    // Get form details with settings
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(`
        id, created_by, workspace_id, published,
        automate_form_settings(
          is_public, accept_responses, copy_allowed,
          thank_you_type, thank_you_data, thank_you_url
        )
      `)
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return { hasPermission: false };
    }

    const settings = form.automate_form_settings?.[0];

    // Check if form is public and published
    if (form.published && settings?.is_public) {
      return { hasPermission: true, form, settings };
    }

    // If user is not provided, only public forms are accessible
    if (!userId) {
      return { hasPermission: false, form, settings };
    }

    // Check if user is the original creator
    if (form.created_by === userId) {
      return { hasPermission: true, form, settings };
    }

    // Check if user has any access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .in("access_type", ["view", "edit", "owner"])
      .single();

    if (!accessError && userAccess) {
      return { hasPermission: true, form, settings };
    }

    return { hasPermission: false, form, settings };
  } catch (err) {
    console.error("Error checking form view permission:", err);
    return { hasPermission: false };
  }
};

/**
 * Check if user has permission to copy a form
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<FormPermissionResult>
 */
export const checkFormCopyPermission = async (
  userId: string,
  formId: string
): Promise<FormPermissionResult> => {
  try {
    // Get form details with settings
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(`
        id, created_by, workspace_id, published,
        automate_form_settings(
          is_public, accept_responses, copy_allowed,
          thank_you_type, thank_you_data, thank_you_url
        )
      `)
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return { hasPermission: false };
    }

    const settings = form.automate_form_settings?.[0];

    // Check if copying is allowed for this form
    if (!settings?.copy_allowed) {
      return { hasPermission: false, form, settings };
    }

    // Check if user is the original creator
    if (form.created_by === userId) {
      return { hasPermission: true, form, settings };
    }

    // Check if user has copy, edit, or owner access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .in("access_type", ["copy", "edit", "owner"])
      .single();

    if (!accessError && userAccess) {
      return { hasPermission: true, form, settings };
    }

    // If form is public and published, allow copying if copy_allowed is true
    if (form.published && settings?.is_public && settings?.copy_allowed) {
      return { hasPermission: true, form, settings };
    }

    return { hasPermission: false, form, settings };
  } catch (err) {
    console.error("Error checking form copy permission:", err);
    return { hasPermission: false };
  }
};

/**
 * Get user's access level for a form
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<{accessType: string | null, isCreator: boolean}>
 */
export const getUserFormAccessLevel = async (
  userId: string,
  formId: string
): Promise<{accessType: string | null, isCreator: boolean}> => {
  try {
    // Check if user is the creator
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("created_by")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return { accessType: null, isCreator: false };
    }

    if (form.created_by === userId) {
      return { accessType: "owner", isCreator: true };
    }

    // Check shared access
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .single();

    if (!accessError && userAccess) {
      return { accessType: userAccess.access_type, isCreator: false };
    }

    return { accessType: null, isCreator: false };
  } catch (err) {
    console.error("Error getting user form access level:", err);
    return { accessType: null, isCreator: false };
  }
};

/**
 * Check if user can perform bulk operations on multiple forms
 * @param userId - The user ID
 * @param formIds - Array of form IDs
 * @param requiredAccess - Array of required access types
 * @returns Promise<{[formId: string]: boolean}>
 */
export const checkBulkFormPermissions = async (
  userId: string,
  formIds: string[],
  requiredAccess: string[]
): Promise<{[formId: string]: boolean}> => {
  const results: {[formId: string]: boolean} = {};

  try {
    // Check permissions for each form
    const permissionChecks = await Promise.all(
      formIds.map(formId => checkFormPermission(userId, formId, requiredAccess))
    );

    formIds.forEach((formId, index) => {
      results[formId] = permissionChecks[index].hasPermission;
    });

    return results;
  } catch (err) {
    console.error("Error checking bulk form permissions:", err);
    // Return false for all forms if error occurs
    formIds.forEach(formId => {
      results[formId] = false;
    });
    return results;
  }
};
