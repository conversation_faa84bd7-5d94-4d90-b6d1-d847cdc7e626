import { supabase } from "../db";

/**
 * Simple utility to check if user can edit a form
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<boolean> - true if user can edit, false otherwise
 */
export const canUserEditForm = async (userId: string, formId: string): Promise<boolean> => {
  try {
    // Get form details
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return false;
    }

    // Check if user is the original creator (can always edit)
    if (form.created_by === userId) {
      return true;
    }

    // Check if user has edit or owner access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .in("access_type", ["edit", "owner"])
      .single();

    // Return true if user has edit or owner access
    return !accessError && !!userAccess;
  } catch (err) {
    console.error("Error checking form edit permission:", err);
    return false;
  }
};

/**
 * Simple utility to check if user can view a form
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<boolean> - true if user can view, false otherwise
 */
export const canUserViewForm = async (userId: string, formId: string): Promise<boolean> => {
  try {
    // Get form details
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return false;
    }

    // Check if user is the original creator (can always view)
    if (form.created_by === userId) {
      return true;
    }

    // Check if user has any access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .in("access_type", ["view", "edit", "owner"])
      .single();

    // Return true if user has any access
    return !accessError && !!userAccess;
  } catch (err) {
    console.error("Error checking form view permission:", err);
    return false;
  }
};

/**
 * Simple utility to check if user can delete a form (only owner)
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<boolean> - true if user can delete, false otherwise
 */
export const canUserDeleteForm = async (userId: string, formId: string): Promise<boolean> => {
  try {
    // Get form details
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return false;
    }

    // Check if user is the original creator (can always delete)
    if (form.created_by === userId) {
      return true;
    }

    // Check if user has owner access through form sharing
    const { data: userAccess, error: accessError } = await supabase
      .from("automate_form_user_access")
      .select("access_type")
      .eq("form_id", formId)
      .eq("user_id", userId)
      .eq("access_type", "owner")
      .single();

    // Return true if user has owner access
    return !accessError && !!userAccess;
  } catch (err) {
    console.error("Error checking form delete permission:", err);
    return false;
  }
};

/**
 * Simple utility to check if user can edit form settings
 * Same as form edit permission - edit/owner access required
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<boolean> - true if user can edit settings, false otherwise
 */
export const canUserEditFormSettings = async (userId: string, formId: string): Promise<boolean> => {
  // Form settings editing has same permission as form editing
  return await canUserEditForm(userId, formId);
};

/**
 * Simple utility to check if user can view form settings
 * Same as form view permission - any access required
 * @param userId - The user ID
 * @param formId - The form ID
 * @returns Promise<boolean> - true if user can view settings, false otherwise
 */
export const canUserViewFormSettings = async (userId: string, formId: string): Promise<boolean> => {
  // Form settings viewing has same permission as form viewing
  return await canUserViewForm(userId, formId);
};


