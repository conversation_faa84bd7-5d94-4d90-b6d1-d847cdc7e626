import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Plan hierarchy for upgrade validation
const PLAN_HIERARCHY: Record<number, { tier: number; name: string }> = {
  8: { tier: 0, name: "Free" },
  11: { tier: 1, name: "bronze"},
  12: { tier: 2, name: "<PERSON>"},
  13: { tier: 3, name: "<PERSON>" },
};

const validateSubscriptionUpgrade = async (workspaceId: number, newModuleId: number, newPlanDuration: string) => {
  try {
    // Get all active subscriptions for the workspace
    const { data: currentSubs, error: currentSubError } = await supabase
      .from("automate_form_module_subscription")
      .select(`
        id,
        module,
        remarks,
        validity,
        app_modules(id, name, code)
      `)
      .eq("workspace_id", workspaceId)
      .eq("status", "active");

    if (currentSubError) {
      console.error("Error fetching current subscriptions:", currentSubError);
      return { valid: false, message: "Failed to retrieve current subscriptions" };
    }

    // If no current subscriptions, allow any purchase
    if (!currentSubs || currentSubs.length === 0) {
      return { valid: true };
    }

    // Check if user already has this exact subscription
    const existingSubscription = currentSubs.find(sub =>
      sub.module === newModuleId
    );

    if (existingSubscription) {
      return {
        valid: false,
        message: "You already have this subscription active."
      };
    }

    // Find the highest tier subscription and longest duration currently active
    let highestCurrentTier = -1;
    let highestCurrentPlan = null;
    let longestDuration = "monthly"; // Start with shortest duration

    // Define duration hierarchy
    const durationHierarchy: Record<string, number> = {
      "monthly": 1,
      "yearly": 12,
      "three_year": 36,
      "five_year": 60
    };

    for (const sub of currentSubs) {
      const currentTier = PLAN_HIERARCHY[sub.module]?.tier ?? 0;

      // Determine plan duration from remarks field
      let currentPlanDuration = "yearly"; // default to yearly
      if (sub.remarks?.includes("monthly")) {
        currentPlanDuration = "monthly";
      } else if (sub.remarks?.includes("three_year")) {
        currentPlanDuration = "three_year";
      } else if (sub.remarks?.includes("five_year")) {
        currentPlanDuration = "five_year";
      }

      if (currentTier > highestCurrentTier) {
        highestCurrentTier = currentTier;
        highestCurrentPlan = sub;
      }

      // Track the longest duration plan user currently has
      if (durationHierarchy[currentPlanDuration] > durationHierarchy[longestDuration]) {
        longestDuration = currentPlanDuration;
      }
    }

    const newTier = PLAN_HIERARCHY[newModuleId]?.tier ?? 0;

    // Check plan tier hierarchy - no downgrades allowed
    if (newTier < highestCurrentTier) {
      return {
        valid: false,
        message: `Cannot downgrade from ${PLAN_HIERARCHY[highestCurrentPlan?.module]?.name || 'current plan'} to ${PLAN_HIERARCHY[newModuleId]?.name || 'selected plan'}. Only upgrades are allowed.`
      };
    }

    // Duration restrictions: users cannot downgrade plan duration
    // If user has 5-year plan, they can only buy yearly/3-year/5-year plans
    // If user has 3-year plan, they can only buy yearly/3-year/5-year plans
    // If user has yearly plan, they can only buy yearly/3-year/5-year plans
    // If user has monthly plan, they can buy any duration

    const newDurationMonths = durationHierarchy[newPlanDuration] || 1;
    const longestDurationMonths = durationHierarchy[longestDuration] || 1;

    if (newDurationMonths < longestDurationMonths) {
      const durationNames: Record<string, string> = {
        "monthly": "Monthly",
        "yearly": "Yearly",
        "three_year": "3-Year",
        "five_year": "5-Year"
      };

      return {
        valid: false,
        message: `You have ${durationNames[longestDuration] || longestDuration} subscriptions active. You cannot downgrade to ${durationNames[newPlanDuration] || newPlanDuration} plans.`
      };
    }

    return { valid: true };
  } catch (err) {
    console.error("Error in validateSubscriptionUpgrade:", err);
    return { valid: false, message: "Failed to validate subscription upgrade" };
  }
};

// Helper function to check if a plan is paid (not free)
const isPaidPlan = (moduleId: number): boolean => {
  return moduleId !== 8;
};

// Helper function to check if workspace has any paid subscription
const hasAnyPaidSubscription = async (workspaceId: number): Promise<boolean> => {
  try {
    const { data: subscriptions, error } = await supabase
      .from("automate_form_module_subscription")
      .select("module")
      .eq("workspace_id", workspaceId)
      .eq("status", "active");

    if (error || !subscriptions) {
      return false;
    }

    // Check if any subscription is a paid plan
    return subscriptions.some(sub => isPaidPlan(sub.module));
  } catch (err) {
    console.error("Error checking paid subscriptions:", err);
    return false;
  }
};

// Get active subscriptions for a workspace
const getWorkspaceSubscriptions = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const { data: subscriptions, error } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id,
        num_users,
        validity,
        status,
        remarks,
        created_at,
        module(id, name, code, caption, features)
      `
      )
      .eq("workspace_id", userdata.workspace_id)
      .eq("status", "active");

    if (error) {
      console.error("Get Subscriptions Error:", error);
      return sendApiError(c, "Failed to retrieve subscriptions", 500);
    }

    return sendApiResponse(c, { subscriptions });
  } catch (err) {
    console.error("Get Subscriptions Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Manually renew a subscription
const renewSubscription = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const subscriptionId = c.req.param("subscription_id");

    // Get subscription details
    const { data: subscription, error: subError } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id,
        module,
        workspace_id,
        num_users,
        validity,
        status,
        remarks
      `
      )
      .eq("id", subscriptionId)
      .single();

    if (subError || !subscription) {
      console.error("Get Subscription Error:", subError);
      return sendApiError(c, "Failed to retrieve subscription", 500);
    }

    // Get module details
    const { data: module, error: moduleError } = await supabase
      .from("app_modules")
      .select("*")
      .eq("id", subscription.module)
      .single();

    if (moduleError || !module) {
      console.error("Get Module Error:", moduleError);
      return sendApiError(c, "Failed to retrieve module information", 500);
    }

    // Determine plan duration from remarks field
    let planDuration = "yearly"; // default to yearly
    if (subscription.remarks?.includes("monthly")) {
      planDuration = "monthly";
    } else if (subscription.remarks?.includes("three_year")) {
      planDuration = "three_year";
    } else if (subscription.remarks?.includes("five_year")) {
      planDuration = "five_year";
    }

    // Calculate renewal price based on plan duration
    let price = 0;
    if (planDuration === "monthly") {
      price = module.monthly_price || 0;
    } else if (planDuration === "yearly") {
      price = module.yearly_price || 0;
    } else if (planDuration === "three_year") {
      price = module.three_year_price || 0;
    } else if (planDuration === "five_year") {
      price = module.five_year_price || 0;
    }

    // Validate price
    if (isNaN(price) || price < 0) {
      return sendApiError(c, "Invalid plan pricing configuration", 400);
    }

    // Check wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from("wallets")
      .select("balance")
      .eq("workspace_id", subscription.workspace_id)
      .single();

    if (walletError || !wallet) {
      console.error("Get Wallet Error:", walletError);
      return sendApiError(c, "Failed to retrieve wallet information", 500);
    }

    // Check if balance is sufficient
    if (wallet.balance < price) {
      return sendApiError(
        c,
        "Insufficient wallet balance. Please recharge your wallet.",
        400
      );
    }

    // Create transaction
    const { error: transactionError } = await supabase
      .from("automate_form_transactions")
      .insert([
        {
          workspace_id: subscription.workspace_id,
          amount: -price,
          currency: "INR",
          status: "success",
          type: "subscription_renewal",
          payment_method: "wallet",
          remarks: `Manual renewal for ${module.name} - ${planDuration} plan`,
        },
      ])
      .select("id")
      .single();

    if (transactionError) {
      console.error("Create Transaction Error:", transactionError);
      return sendApiError(c, "Failed to create transaction", 500);
    }

    // Calculate new validity date based on plan duration
    const validityDate = new Date(subscription.validity);
    if (planDuration === "monthly") {
      validityDate.setMonth(validityDate.getMonth() + 1);
    } else if (planDuration === "yearly") {
      validityDate.setFullYear(validityDate.getFullYear() + 1);
    } else if (planDuration === "three_year") {
      validityDate.setFullYear(validityDate.getFullYear() + 3);
    } else if (planDuration === "five_year") {
      validityDate.setFullYear(validityDate.getFullYear() + 5);
    }

    // Update subscription
    const { data: updatedSub, error: updateError } = await supabase
      .from("automate_form_module_subscription")
      .update({
        validity: validityDate.toISOString(),
        status: "active",
        updated_at: new Date().toISOString(),
        updated_by: user.user.id,
      })
      .eq("id", subscriptionId)
      .select()
      .single();

    if (updateError) {
      console.error("Update Subscription Error:", updateError);
      return sendApiError(c, "Failed to update subscription", 500);
    }

    return sendApiResponse(c, {
      message: "Subscription renewed successfully",
      subscription: updatedSub,
    });
  } catch (err) {
    console.error("Renew Subscription Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const purchaseSubscription = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const body = await c.req.json();
    const { module_id, plan_duration } = body;

    // Validate subscription upgrade before proceeding
    const upgradeValidation = await validateSubscriptionUpgrade(
      userdata.workspace_id,
      module_id,
      plan_duration
    );

    if (!upgradeValidation.valid) {
      return sendApiError(c, upgradeValidation.message || "Upgrade validation failed", 400);
    }

    // Validate module
    const { data: module, error: moduleError } = await supabase
      .from("app_modules")
      .select("id, name, yearly_price, monthly_price,three_year_price,five_year_price,app")
      .eq("id", module_id)
      .single();

    if (moduleError || !module) {
      return sendApiError(c, "Invalid module", 400);
    }
    let price = 0;
    if (plan_duration === "monthly") {
      price = module.monthly_price || 0;
    } else if (plan_duration === "yearly") {
      price = module.yearly_price || 0;
    } else if (plan_duration === "three_year") {
      price = module.three_year_price || 0;
    } else if (plan_duration === "five_year") {
      price = module.five_year_price || 0;
    }


    // Validate plan duration
    if (isNaN(price) || price < 0) {
      return sendApiError(c, "Invalid plan pricing configuration", 400);
    }

    // Check wallet balance
    const { data: wallet, error: walletError } = await supabase
      .from("wallets")
      .select("balance")
      .eq("workspace_id", userdata.workspace_id)
      .single();

    if (walletError || !wallet) {
      console.error("Get Wallet Error:", walletError);
      return sendApiError(c, "Failed to retrieve wallet information", 500);
    }
console.log("Wallet balance:", wallet.balance, "Price:", price);
    // Check if balance is sufficient
    if (wallet.balance < price) {
      return sendApiError(
        c,
        "Insufficient wallet balance. Please recharge your wallet.",
        400
      );
    }
  
    // Create transaction record
    const { error: transactionError } = await supabase
      .from("automate_form_transactions")
      .insert([
        {
          workspace_id: userdata.workspace_id, // Fixed: use correct workspace_id
          amount: price,
          currency: "INR",
          status: "success",
          type: "subscription",
          payment_method: "wallet",
          remarks: `Purchase of ${module.name} - ${plan_duration} plan`,
        },
      ]);

    if (transactionError) {
      console.error("Create Transaction Error:", transactionError);
      return sendApiError(c, "Failed to create transaction", 500);
    }

    // Calculate validity date based on plan duration
    const validityDate = new Date();
    if (plan_duration === "monthly") {
      validityDate.setMonth(validityDate.getMonth() + 1);
    } else if (plan_duration === "yearly") {
      validityDate.setFullYear(validityDate.getFullYear() + 1);
    } else if (plan_duration === "three_year") {
      validityDate.setFullYear(validityDate.getFullYear() + 3);
    } else if (plan_duration === "five_year") {
      validityDate.setFullYear(validityDate.getFullYear() + 5);
    }

    // Note: We don't deactivate existing subscriptions anymore
    // Users can have multiple active subscriptions
    // Only deactivate if they're purchasing the exact same subscription (which is already prevented by validation)

    // Create new subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("automate_form_module_subscription")
      .insert([
        {
          workspace_id: userdata.workspace_id, // Fixed: use correct workspace_id
          module: module.id,
          num_users: null, // For paid plans, unlimited users
          validity: validityDate.toISOString(),
          status: "active",
          remarks: `Purchased ${module.name} - ${plan_duration} plan`,
          app:module.app,
          updated_by: user.user.id,
        },
      ])
      .select()
      .single();

    if (subscriptionError) {
      console.error("Create Subscription Error:", subscriptionError);
      return sendApiError(c, "Failed to create subscription", 500);
    }

  
    return sendApiResponse(c, {
      message: "Subscription purchased successfully",
      subscription,
    });
  } catch (err) {
    console.error("Purchase Subscription Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};




export {
  getWorkspaceSubscriptions,
  renewSubscription,
  purchaseSubscription,
  isPaidPlan,
  validateSubscriptionUpgrade,
  hasAnyPaidSubscription
};
