import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { checkFormPermission } from '../utils/FormPermissionUtils';


const getFormFields = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("form_id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }
    console.log("📢 Form ID:", formId);

    // Check if user has permission to view this form
    const permissionCheck = await checkFormPermission(user.user.id, formId, ["view", "edit", "owner"]);

    if (!permissionCheck.hasPermission) {
      return sendApiError(c, "You don't have permission to view this form's fields", 403);
    }

    if (!permissionCheck.form) {
      return sendApiError(c, "Form not found", 404);
    }

    const { data, error } = await supabase
      .from("automate_form_fields")
      .select("id, form_id, fields, created_at, updated_at, condition")
      .eq("form_id", formId)
      .single();
    console.log("📢 Form Fields Data:", data);

    if (error) {
      // Check if it's a not found error
      if (error.code === 'PGRST116') {
        console.log("No fields found for form ID:", formId);
        return sendApiResponse(c, {
          message: "No fields available for this form",
          fields: null,
          form_id: formId
        }, 200);
      }

      console.error("Get Form Fields Error:", error);
      return sendApiError(c, "Failed to fetch form fields", 500);
    }

    if (!data) {
      return sendApiResponse(c, {
        message: "No fields available for this form",
        fields: null,
        form_id: formId
      }, 200);
    }

    return sendApiResponse(c, data);

  } catch (err) {
    console.error("Unexpected Error in Fetching Form Fields:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


const createFormField = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    console.log("📢 Received form field data:", body);

    const { form_id, fields } = body;

    if (!form_id || !fields) {
      return sendApiError(c, "Form ID and fields data are required", 400);
    }

    // Check if user has permission to edit this form
    const permissionCheck = await checkFormPermission(user.user.id, form_id, ["edit", "owner"]);

    if (!permissionCheck.hasPermission) {
      return sendApiError(c, "You don't have permission to create fields for this form", 403);
    }

    if (!permissionCheck.form) {
      return sendApiError(c, "Form not found", 404);
    }

    const newFieldEntry = {
      form_id,
      fields,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("automate_form_fields")
      .insert([newFieldEntry])
      .select("id, form_id, fields");

    if (error || !data || data.length === 0) {
      console.error("Create Form Fields Error:", error);
      return sendApiError(c, "Failed to create form fields", 500);
    }

    return sendApiResponse(c, { form_fields: data[0] }, 201);

  } catch (err) {
    console.error("Create Form Fields Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


const updateFormField = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) {
        return sendApiError(c, "Unauthorized", 401);
      }

      const form_id = c.req.param("id");
      if (!form_id) {
        return sendApiError(c, "Form ID is required", 400);
      }

      console.log("📢 Field ID:", form_id);

      const body = await c.req.json();
      console.log("📢 Update Form Field Data:", body);

      if (!body.fields) {
        return sendApiError(c, "Fields data is required for update", 400);
      }
     
      // Check if user has permission to edit this form
      const permissionCheck = await checkFormPermission(user.user.id, form_id, ["edit", "owner"]);

      if (!permissionCheck.hasPermission) {
        return sendApiError(c, "You don't have permission to edit this form's fields", 403);
      }

      if (!permissionCheck.form) {
        return sendApiError(c, "Form not found", 404);
      }
     const { data: fieldEntry, error: fieldError } = await supabase
        .from("automate_form_fields")
        .select("id")
        .eq("form_id", form_id)
        .single();

      if (fieldError || !fieldEntry) {
        return sendApiError(c, "Field entry not found", 404);
      }

      const updatedFields = {
        fields: body.fields,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from("automate_form_fields")
        .update(updatedFields)
        .eq("id", fieldEntry.id)
        .select();

      if (error) {
        console.error(" Update Form Field Error:", error);
        return sendApiError(c, "Failed to update form fields", 500);
      }

      if (!data || data.length === 0) {
        return sendApiError(c, "No changes were made", 400);
      }

      console.log("✅ Updated Field Entry:", data[0]);

      return sendApiResponse(c, { updated_field_entry: data[0] }, 200);

    } catch (err) {
      console.error("Update Form Fields Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };


  const updateconditionFormField = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) {
        return sendApiError(c, "Unauthorized", 401);
      }

      const form_id = c.req.param("id");
      if (!form_id) {
        return sendApiError(c, "Form ID is required", 400);
      }

      console.log("📢 Field ID:", form_id);

      const body = await c.req.json();
      console.log("📢 Update Form Field Data:", body);

      if (!body.condition) {
        return sendApiError(c, "Condition data is required for update", 400);
      }
      const condition = body?.condition;
      if (condition !== undefined && !Array.isArray(condition)) {
        return sendApiError(c, "Conditions must be a valid array", 400);
      }
      // Check if user has permission to edit this form
      const permissionCheck = await checkFormPermission(user.user.id, form_id, ["edit", "owner"]);

      if (!permissionCheck.hasPermission) {
        return sendApiError(c, "You don't have permission to edit this form's fields", 403);
      }

      if (!permissionCheck.form) {
        return sendApiError(c, "Form not found", 404);
      }

     const { data: fieldEntry, error: fieldError } = await supabase
        .from("automate_form_fields")
        .select("id")
        .eq("form_id", form_id)
        .single();

      if (fieldError || !fieldEntry) {
        return sendApiError(c, "Field entry not found", 404);
      }

      const updatedFields = {
        updated_at: new Date().toISOString(),
        condition :condition || null,
      };

      const { data, error } = await supabase
        .from("automate_form_fields")
        .update(updatedFields)
        .eq("id", fieldEntry.id)
        .select('id,form_id,condition,updated_at');

      if (error) {
        console.error(" Update Form Field Error:", error);
        return sendApiError(c, "Failed to update form fields", 500);
      }

      if (!data || data.length === 0) {
        return sendApiError(c, "No changes were made", 400);
      }

    console.log('updated_field_entry',data[0])

      return sendApiResponse(c, { updated_condition: data[0] }, 200);

    } catch (err) {
      console.error("Update Form Fields Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };
export {  getFormFields, createFormField, updateFormField,updateconditionFormField};









