import { WebClient } from '@slack/web-api';
import { supabase } from '../db';

interface SlackCredentials {
  access_token: string;
  team_id: string;
  team_name: string;
  bot_user_id?: string;
  app_id?: string;
}

interface SlackChannel {
  id: string;
  name: string;
  is_channel: boolean;
  is_group: boolean;
  is_im: boolean;
  is_private: boolean;
  is_archived: boolean;
  is_member: boolean;
}

interface SlackUser {
  id: string;
  name: string;
  real_name: string;
  display_name: string;
  is_bot: boolean;
  deleted: boolean;
}

interface SlackMessageBlock {
  type: string;
  text?: {
    type: string;
    text: string;
  };
  fields?: Array<{
    type: string;
    text: string;
  }>;
}

/**
 * Get valid Slack access token from credentials
 */
export const getValidSlackToken = async (credentialId: string): Promise<string | null> => {
  try {
    const { data: credential, error } = await supabase
      .from('automate_form_integration_credentials')
      .select('auth_data')
      .eq('id', credentialId)
      .single();

    if (error || !credential) {
      console.error('Failed to fetch Slack credentials:', error);
      return null;
    }

    const authData = credential.auth_data as SlackCredentials;
    return authData.access_token || null;
  } catch (error) {
    console.error('Error getting Slack token:', error);
    return null;
  }
};

/**
 * Initialize Slack Web API client
 */
export const createSlackClient = (token: string): WebClient => {
  return new WebClient(token);
};

/**
 * Get Slack channels for a workspace
 */
export const getSlackChannels = async (token: string): Promise<SlackChannel[]> => {
  try {
    const client = createSlackClient(token);
    
    // Get public channels
    const publicChannels = await client.conversations.list({
      types: 'public_channel',
      exclude_archived: true,
      limit: 200
    });

    // Get private channels the bot is a member of
    const privateChannels = await client.conversations.list({
      types: 'private_channel',
      exclude_archived: true,
      limit: 200
    });

    const allChannels = [
      ...(publicChannels.channels || []),
      ...(privateChannels.channels || [])
    ];

    return allChannels.map(channel => ({
      id: channel.id!,
      name: channel.name!,
      is_channel: channel.is_channel || false,
      is_group: channel.is_group || false,
      is_im: channel.is_im || false,
      is_private: channel.is_private || false,
      is_archived: channel.is_archived || false,
      is_member: channel.is_member || false
    }));
  } catch (error) {
    console.error('Error fetching Slack channels:', error);
    throw error;
  }
};

/**
 * Get Slack users for a workspace
 */
export const getSlackUsers = async (token: string): Promise<SlackUser[]> => {
  try {
    const client = createSlackClient(token);
    
    const response = await client.users.list({
      limit: 200
    });

    if (!response.members) {
      return [];
    }

    return response.members
      .filter(user => !user.deleted && !user.is_bot)
      .map(user => ({
        id: user.id!,
        name: user.name!,
        real_name: user.real_name || user.name!,
        display_name: user.profile?.display_name || user.real_name || user.name!,
        is_bot: user.is_bot || false,
        deleted: user.deleted || false
      }));
  } catch (error) {
    console.error('Error fetching Slack users:', error);
    throw error;
  }
};

/**
 * Send a message to Slack channel
 */
// export const sendSlackMessage = async (
//   token: string,
//   channel: string,
//   text: string,
//   blocks?: SlackMessageBlock[]
// ): Promise<boolean> => {
//   try {
//     const client = createSlackClient(token);
    
//     const messageOptions: any = {
//       channel,
//       text
//     };

//     if (blocks && blocks.length > 0) {
//       messageOptions.blocks = blocks;
//     }

//     const response = await client.chat.postMessage(messageOptions);
    
//     return response.ok || false;
//   } catch (error) {
//     console.error('Error sending Slack message:', error);
//     return false;
//   }
// };
export const sendSlackMessage = async (
  accessToken: string,
  channelId: string,
  text: string,
  blocks?: any
) => {
  try {
    const client = new WebClient(accessToken);
    console.log("Attempting to send message to channel:", channelId);

    // First try to join the channel
    try {
      const joinResponse = await client.conversations.join({
        channel: channelId,
      });
      console.log("Channel join response:", joinResponse);
    } catch (joinError: any) {
      // If it's a private channel or DM, joining will fail but we can still try to send
      console.log("Channel join attempt result:", joinError.message);
    }

    // Try to get channel info to verify access
    try {
      await client.conversations.info({
        channel: channelId
      });
    } catch (infoError: any) {
      console.error("Cannot access channel:", infoError.message);
      if (infoError.message.includes("missing_scope")) {
        throw new Error("Bot needs additional permissions. Please reinstall the app with required scopes.");
      }
      throw new Error("Cannot access channel. Make sure the bot is invited to the channel.");
    }

    // Then send the message
    const result = await client.chat.postMessage({
      channel: channelId,
      text: text,
      blocks: blocks,
      unfurl_links: false,
      unfurl_media: false,
    });

    console.log("Message send result:", result.ok);
    return result.ok;
  } catch (error: any) {
    console.error("Error in sendSlackMessage:", error.message);
    throw error; // Propagate the error with meaningful message
  }
};

/**
 * Send a direct message to a Slack user
 */
export const sendSlackMessageToUser = async (
  accessToken: string,
  userId: string,
  text: string,
  blocks?: any
) => {
  try {
    const client = new WebClient(accessToken);
    console.log("Attempting to send DM to user:", userId);

    // First open a DM channel with the user
    try {
      const dmResponse = await client.conversations.open({
        users: userId
      });

      if (!dmResponse.ok || !dmResponse.channel?.id) {
        throw new Error("Failed to open DM channel");
      }

      const dmChannelId = dmResponse.channel.id;
      console.log("Opened DM channel:", dmChannelId);

      // Send the message to the DM channel
      const result = await client.chat.postMessage({
        channel: dmChannelId,
        text: text,
        blocks: blocks,
        unfurl_links: false,
        unfurl_media: false,
      });

      console.log("DM send result:", result.ok);
      return result.ok;
    } catch (error: any) {
      console.error("Error sending DM:", error.message);
      if (error.message.includes("missing_scope")) {
        throw new Error("Bot needs im:write and chat:write permissions. Please reinstall the app.");
      } else if (error.message.includes("cannot_dm_bot")) {
        throw new Error("Cannot send DM to a bot user.");
      } else if (error.message.includes("user_not_found")) {
        throw new Error("User not found in the workspace.");
      }
      throw error;
    }
  } catch (error: any) {
    console.error("Error in sendSlackMessageToUser:", error.message);
    throw error;
  }
};

/**
 * Convert HTML to Slack-friendly plain text
 */
const htmlToSlackText = (html: string): string => {
  if (!html || typeof html !== 'string') {
    return html;
  }

  return html
    // Convert common HTML tags to Slack markdown
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '*$1*')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '*$1*')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '_$1_')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '_$1_')
    .replace(/<u[^>]*>(.*?)<\/u>/gi, '$1')
    .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
    .replace(/<pre[^>]*>(.*?)<\/pre>/gi, '```$1```')

    // Convert line breaks
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<p[^>]*>/gi, '')

    // Convert lists
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '• $1\n')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<ul[^>]*>/gi, '')
    .replace(/<\/ol>/gi, '\n')
    .replace(/<ol[^>]*>/gi, '')

    // Convert headings
    .replace(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi, '*$1*\n')

    // Convert links
    .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '<$1|$2>')

    // Remove all other HTML tags
    .replace(/<[^>]*>/g, '')

    // Clean up extra whitespace and newlines
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .replace(/^\s+|\s+$/g, '')
    .trim();
};

/**
 * Format value for Slack display
 */
const formatValueForSlack = (value: any): string => {
  if (value === null || value === undefined) {
    return 'N/A';
  }

  if (typeof value === 'object') {
    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(item =>
        typeof item === 'string' ? htmlToSlackText(item) : String(item)
      ).join(', ');
    }

    // Handle objects (like name fields)
    if (value.firstName && value.lastName) {
      return `${value.firstName} ${value.lastName}`;
    }

    return JSON.stringify(value);
  }

  if (typeof value === 'string') {
    return htmlToSlackText(value);
  }

  return String(value);
};

/**
 * Create formatted Slack message blocks from form data
 * Clean format - shows actual form response content with submission time in workspace timezone
 */
export const createSlackMessageBlocks = (
  formTitle: string,
  processed: Record<string, any>,
  submittedAt: string,
  timezone: string = 'Asia/Kolkata'
): SlackMessageBlock[] => {
  // Create clean form data text - just the actual content
  const formDataText = Object.entries(processed)
    .map(([_key, value]) => {
      const cleanValue = formatValueForSlack(value);
      return cleanValue; // Just return the clean value without field labels
    })
    .join('\n\n'); // Separate multiple fields with double line break

  // Format date with workspace timezone
  const submittedDate = new Date(submittedAt);
  const formattedDate = submittedDate.toLocaleString('en-US', {
    timeZone: timezone,
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  });

  return [
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `${formDataText}\n\n*Submitted At:* ${formattedDate}`
      }
    }
  ];
};





