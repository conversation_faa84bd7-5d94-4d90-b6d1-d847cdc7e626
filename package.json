{"name": "bun-backend", "module": "index.ts", "type": "module", "scripts": {"start": "bun run src/index.ts", "dev": "bun run --watch src/index.ts"}, "devDependencies": {"@types/bun": "latest", "prettier": "^3.5.1"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@slack/web-api": "^7.9.2", "@supabase/supabase-js": "^2.48.1", "@types/bcrypt": "^5.0.2", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.8", "axios": "^1.8.4", "bcrypt": "^5.1.1", "dotenv": "^16.4.7", "googleapis": "^146.0.0", "hono": "^4.7.1", "i": "^0.3.7", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "uuid": "^11.1.0"}}