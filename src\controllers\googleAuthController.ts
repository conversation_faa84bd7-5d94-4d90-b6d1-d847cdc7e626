import dotenv from "dotenv";
dotenv.config({ path: "./.env" });
import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { google } from "googleapis";
import { form } from "../routes/form.routes";

const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;

const oauth2Client = new google.auth.OAuth2(
  CLIENT_ID,
  CLIENT_SECRET,
  REDIRECT_URI
);
const scope: any = {
  "96e706d1-a0b6-491c-9eb2-fc97603edcab": [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/userinfo.email"
  ],
  "f5732279-8247-49a2-8453-501386423825": [
    "https://www.googleapis.com/auth/gmail.send",
    "https://www.googleapis.com/auth/userinfo.email",
  ],
};


const addUserConnection = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const userId =user.user.id;
    const { redirect_uri, integration_id, formId, formType, name,action_id } =
      c.req.query();
     

    if (!integration_id || !redirect_uri || !name || !formId || !formType ||!action_id) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Validate integration
    const { data: integration, error: integrationError } = await supabase
      .from("automate_form_integrations")
      .select("name")
      .eq("id", integration_id)
      .single();

    if (integrationError || !integration) {
      console.error("Integration Error:", integrationError);
      return sendApiError(c, "Failed to retrieve integration", 500);
    }

    // Generate authentication URL if not authenticated
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      prompt: "consent",
      scope:scope[integration_id],
      state: JSON.stringify({
        userId,
        redirect_uri,
        integration_id,
        formId,
        formType,
        name,
         action_id,
        integration_type: 'sheet'
      }),
    });
    return sendApiResponse(c,{authUrl});
  } catch (err) {
    console.error("Google Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const googleAuthCallback = async (c: Context) => {
  try {
    const { code, state } = c.req.query();
    if (!code || !state) return sendApiError(c, "Missing code or state", 400);

    const { userId, redirect_uri, formId,formType ,integration_id,name,integration_type,action_id} = JSON.parse(state);

    const cleanRedirectUri = redirect_uri.replace(/^['"]+|['"]+$/g, '');
    const failRedirectUrl = `${cleanRedirectUri}?formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&success=false`;
    if (!userId || !cleanRedirectUri) {
      return c.redirect(failRedirectUrl);
    }
    let tokens;
    try {
      const response = await oauth2Client.getToken(code);
      tokens = response.tokens;
    } catch (tokenError) {
      console.error("Error fetching tokens:", tokenError);
      return c.redirect(failRedirectUrl);
    }

    if (!tokens.access_token) {
      return c.redirect(failRedirectUrl);
    }

    if (!tokens.refresh_token) {
      console.warn("No refresh token provided by Google.");
    }

    oauth2Client.setCredentials(tokens);
    console.log("Tokens:", tokens);

    // Get user info from Google
    const oauth2 = google.oauth2({ auth: oauth2Client, version: "v2" });
    let userInfo;
    try {
      const response = await oauth2.userinfo.get();
      userInfo = response.data;
    } catch (userInfoError) {
      console.error("Error fetching user info:", userInfoError);
   
      return c.redirect(failRedirectUrl);
    }

    const googleEmail = userInfo?.email || null;
    if (!googleEmail) {
      
      return c.redirect(failRedirectUrl);
    }

    // Fetch user from database
    const { data: user, error: userError } = await supabase
      .from("user_profile")
      .select("id, email, workspace_id")
      .eq("id", userId)
      .single();

    if (userError || !user) {
      console.error("Database error or user not found:", userError);
      return c.redirect(failRedirectUrl)
    }
    const {data: insertedData, error: insertError } = await supabase
    .from("automate_form_integration_credentials")
    .insert({
      name:name,
      created_by: userId,
      workspace_id: user.workspace_id || null,
      integration_id,
      auth_type: "OAuth2",
      auth_data: {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || null,
        expiry_date: tokens.expiry_date || null,
        email: googleEmail,
        name: userInfo?.name || null,
      },
      created_at: new Date().toISOString(),
    }).select("id")
    .single();

  if (insertError || !insertedData) {
    console.error("Error inserting credentials:", insertError);
    return c.redirect(failRedirectUrl);
  }
  const credentialId = insertedData.id;

  const finalRedirectUrl = `${cleanRedirectUri}?formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&integration_type=${integration_type}&success=true&credential_id=${credentialId}&integration_id=${integration_id}&action_id=${action_id}`;
  console.log("Redirecting to:", finalRedirectUrl);
    return c.redirect(finalRedirectUrl);
  } catch (err) {
    console.error("Google Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const test = async (c: Context) => {
  try {
     const userId = 'f0b27bf4-d1be-4106-8840-0bf0b78d1ec7'
    const { redirect_uri, integration_id, formId, formType, name } =
      c.req.query();

console.log("Query Params:", c.req.query());
    console.log("User ID:", userId);
    console.log("Redirect URI:", redirect_uri);
    console.log("Integration ID:", integration_id);
    console.log("Form ID:", formId);
    console.log("Form Type:", formType);
    console.log("Name:", name);
    // if (!integration_id || !redirect_uri || !name || !formId || !formType) {
    //   return sendApiError(c, "Missing required parameters", 400);
    // }

    // Validate integration
    const { data: integration, error: integrationError } = await supabase
      .from("automate_form_integrations")
      .select("name")
      .eq("id", integration_id)
      .single();

    if (integrationError || !integration) {
      console.error("Integration Error:", integrationError);
      return sendApiError(c, "Failed to retrieve integration", 500);
    }

    // Generate authentication URL if not authenticated
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: "offline",
      prompt: "consent",
      scope:scope[integration_id],
      state: JSON.stringify({
        userId,
        redirect_uri,
        integration_id,
        formId,
        formType,
        name,
      }),
    });
    return c.redirect(authUrl);
  } catch (err) {
    console.error("Google Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
export { googleAuthCallback,addUserConnection,test };
