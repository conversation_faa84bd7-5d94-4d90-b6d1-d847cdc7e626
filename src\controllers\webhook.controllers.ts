import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { google } from "googleapis";
import { getValidGoogleToken } from "../utils/GoogleToken";
import { mergeFieldsWithAnswers } from "../utils/comman";
import {
  getValidSlackToken,
  sendSlackMessage,
  sendSlackMessageToUser,
  createSlackMessageBlocks,
  createSlackMessageText,
} from "../utils/slackHelper";
import {
  getValidGmailToken,
  sendGmailMessage,
  processEmailTemplate,
  createDefaultEmailTemplate,
  extractEmailFromFormData,
} from "../utils/gmailHelper";
// import {
//   getValidOutlookToken,
//   sendOutlookMessage,
//   processEmailTemplate as processOutlookEmailTemplate,
//   createDefaultEmailTemplate as createOutlookDefaultEmailTemplate,
//   extractEmailFromFormData as extractOutlookEmailFromFormData,
// } from "../utils/outlookHelper";
import axios from "axios";
import { getValidOutlookToken, sendOutlookMessage } from "../utils/outlookHelper";
const CRM_API_URL = "https://api.automatebusiness.com/functions/v1/createLead";

type FormField = {
  id: any;
  name: any;
  type: any;
  component: any;
  value: any;
  [key: string]: any; // Index Signature for dynamic properties
};

// Type definitions
interface WebhookLogEntry {
  id: number;
}

interface FormRecord {
  id: string;
  form_id: string;
  answers: Answer[];
}

interface Answer {
  id: string;
  value: string | NameValue | null;
}

interface NameValue {
  firstName: string;
  lastName: string;
}

interface FormFields {
  fields: any[];
}

interface FormIntegration {
  metadata: IntegrationMetadata;
  credential_id: string;
  mapped_data: MappedData[];
}

interface IntegrationMetadata {
  category_id: number;
  Source: string;
  Stage: string;
  assignedTo: string;
  pipelineId: string;
}

interface MappedData {
  id: string;
  name: string;
}
function processDataMapping(mappedData: any, responseData: any) {
  console.log("mappedData", mappedData);
  console.log("responseData", responseData);
  const result: any = {};
  // Response data ko easily access karne ke liye object banate hain
  const responseMap: any = {};
  responseData.forEach((item: any) => {
    responseMap[item.id] = item;
  });
  mappedData.forEach((mappedItem: any) => {
    const { id, key } = mappedItem;
    let processedValue = key;
    // Curly braces ke andar ka content find karte hain
    const regex = /\{\{([^}]+)\}\}/g;
    processedValue = processedValue.replace(
      regex,
      (match: any, content: any) => {
        // Content ko parts mein split karte hain
        const parts = content.split(".");
        if (parts.length >= 1) {
          const responseId = parts[0];
          const responseItem = responseMap[responseId];
          if (responseItem) {
            // Agar nested value hai (jaise firstName, lastName)
            if (parts.length > 2) {
              const nestedKey = parts[2];
              if (
                responseItem.value &&
                typeof responseItem.value === "object"
              ) {
                return responseItem.value[nestedKey] || "";
              }
            } else if (parts.length === 1) {
              return responseItem.value || "";
            } else if (parts.length === 2 && parts[1] === "fullname") {
              return responseItem.value || "";
            } else if (parts.length >= 2) {
              const propertyName = parts[parts.length - 1];
              if (
                responseItem.value &&
                typeof responseItem.value === "object"
              ) {
                return responseItem.value[propertyName] || "";
              }
              return responseItem.value || "";
            }
          }
        }
        return ""; // Agar kuch nahi mila to empty string return karte hain
      }
    );
    result[id] = processedValue;
  });
  return result;
}

const sendDataToGoogleSheet = async (c: Context) => {
  let logEntry: { id: number } | null = null;

  try {
    // 1) Parse the request body
    const body = await c.req.json();

    if (!body?.record || !body.record.form_id || !body.record.answers) {
      throw new Error("Invalid request payload: Missing form_id or answers");
    }

    const { form_id, answers } = body.record;
    console.log("🚀 Submitting Form to Google Sheets...,answers:", answers);

    // 2) Insert a row in webhook_execution_logs to track this request
    const { data: logData, error: logError } = await supabase
      .from("automate_form_webhook_execution_logs")
      .insert({
        response_id: body.record.id,
        data_sent: body.record,
        status: "pending",
      })
      .select("id")
      .single();

    if (logError) {
      console.error(
        "Failed to create webhook execution log:",
        logError.message
      );
    } else {
      logEntry = logData;
    }

    // 3) Fetch Google Sheets integration for the form
    const GOOGLE_SHEET_INTEGRATION_ID = "96e706d1-a0b6-491c-9eb2-fc97603edcab";
    const { data: formIntegrations, error: formIntegrationError } =
      await supabase
        .from("form_integrations")
        .select("metadata, credential_id, enabled,mapped_data,action_id")
        .eq("form_id", form_id)
        .eq("integration_id", GOOGLE_SHEET_INTEGRATION_ID)
        .single();

    if (formIntegrationError || !formIntegrations) {
      console.error(
        "No Google Sheets integration found:",
        formIntegrationError?.message || "No integrations available"
      );
      return sendApiError(c, "Google Sheets integration not configured", 400);
    }

    // Check if the integration is enabled
    if (!formIntegrations.enabled) {
      console.error("Google Sheets integration is disabled");
      return sendApiError(c, "Google Sheets integration is disabled", 400);
    }

    // Process the integration
    const spreadsheetId = formIntegrations.metadata?.spreadsheetId;
    const sheetName = formIntegrations.metadata?.sheetName || "Sheet1";
    const credentialId = formIntegrations.credential_id;
    const mapped_data = formIntegrations.mapped_data;
    const accessToken: any = await getValidGoogleToken(credentialId);
    if (!spreadsheetId) {
      console.error("No spreadsheet linked for this integration.");
      return sendApiError(
        c,
        "No spreadsheet configured for this integration",
        400
      );
    }

    if (!accessToken) {
      console.error(
        `Failed to retrieve Google access token for credential: ${credentialId}`
      );
      return sendApiError(c, "Failed to authenticate with Google", 401);
    }
    console.log(
      `📄 Processing Google Sheet Integration for: ${spreadsheetId}, Sheet: ${sheetName}`
    );

    if (formIntegrations.action_id === "56aa4bb0-b71d-4bc2-a857-ab13c087b4f7") {
      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({ access_token: accessToken });
      const sheets = google.sheets({ version: "v4", auth: oauth2Client });

      // Retrieve existing column headers (first row)
      const sheetResponse = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!A1:AZ1`,
      });

      let existingHeaders = sheetResponse.data.values?.[0] || [];
      console.log("📑 Existing Headers:", existingHeaders);

      // --- MAPPED DATA LOGIC (like linkFormToGoogleSheet) ---
      let mappedHeaders = Array.isArray(mapped_data)
        ? mapped_data.map((item: any) => item.title || item.name)
        : [];
      if (!mappedHeaders.includes("Submitted At")) {
        mappedHeaders.push("Submitted At");
      }

      // Use processDataMapping function like in linkFormToGoogleSheet
      const processedData = processDataMapping(mapped_data, answers);
      console.log("Processed data for response:", processedData);

      // Convert processed data to array format based on mapped headers order
      const rowData = mappedHeaders.map((header: any) => {
        // Handle "Submitted At" column specially
        if (header === "Submitted At") {
          return body.record.submitted_at || new Date().toISOString();
        }

        // Find the corresponding mapped item
        const mappedItem = mapped_data.find(
          (item: any) => (item.title || item.name) === header
        );
        if (mappedItem) {
          return processedData[mappedItem.id] || "";
        }
        return "";
      });
      // --- END MAPPED DATA LOGIC ---

      // Update the sheet headers if there are any new ones
      if (
        mappedHeaders.length > existingHeaders.length ||
        mappedHeaders.some((h, i) => existingHeaders[i] !== h)
      ) {
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: `${sheetName}!A1:AZ1`,
          valueInputOption: "RAW",
          requestBody: {
            values: [mappedHeaders],
          },
        });
        console.log("✅ Updated Headers in Google Sheets");
      }

      // Append the new row of data
      await sheets.spreadsheets.values.append({
        spreadsheetId,
        range: `${sheetName}!A2:AZ`,
        valueInputOption: "RAW",
        requestBody: {
          values: [rowData],
        },
      });
      console.log("✅ Data appended to Google Sheets");

      // If everything goes well, update webhook_execution_logs with a success message and status
      if (logEntry) {
        await supabase
          .from("automate_form_webhook_execution_logs")
          .update({
            response: "Successfully submitted to Google Sheets",
            status: "success",
          })
          .eq("id", logEntry.id);
      }

      // Send a success response back to the caller
      return sendApiResponse(c, {
        message: "Successfully submitted form response to Google Sheets",
        data: body,
      });
    } else if(formIntegrations.action_id === "3ab6f8e8-aaa8-466c-b9fa-8655e6d77f1c") {
      if (!accessToken) {
        console.error(
          `Failed to retrieve Google access token for credential: ${credentialId}`
        );
        return sendApiError(c, "Failed to authenticate with Google", 401);
      }

      // Configure the Google Sheets client
      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({ access_token: accessToken });
      const sheets = google.sheets({ version: "v4", auth: oauth2Client });

      // Retrieve existing column headers (first row)
      const sheetResponse = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!A1:ZZ1`,
      });

      let existingHeaders = sheetResponse.data.values?.[0] || [];
      console.log("📑 Existing Headers:", existingHeaders);

      // Prepare new headers and row data
      let newHeaders = [...existingHeaders];
      let rowData: any[] = [];

      // Use the title field as the header and value as the data
      answers.forEach((answer: any) => {
        if (typeof answer.value === "object" && answer.value !== null) {
          // Handle name fields with firstName and lastName
          if ("firstName" in answer.value && "lastName" in answer.value) {
            // First Name
            const firstNameTitle = answer.firstNameTitle || "First Name";
            if (!newHeaders.includes(firstNameTitle)) {
              newHeaders.push(firstNameTitle);
            }
            rowData[newHeaders.indexOf(firstNameTitle)] =
              answer.value.firstName;

            // Last Name
            const lastNameTitle = answer.lastNameTitle || "Last Name";
            if (!newHeaders.includes(lastNameTitle)) {
              newHeaders.push(lastNameTitle);
            }
            rowData[newHeaders.indexOf(lastNameTitle)] = answer.value.lastName;
          } else {
            // Handle other object values
            Object.entries(answer.value).forEach(([key, value]) => {
              const columnTitle = answer[`${key}Title`] || key;
              if (!newHeaders.includes(columnTitle)) {
                newHeaders.push(columnTitle);
              }
              rowData[newHeaders.indexOf(columnTitle)] = value;
            });
          }
        } else {
          // Use title as the header if available, otherwise use name
          const headerName = answer.title || answer.name;
          if (!newHeaders.includes(headerName)) {
            newHeaders.push(headerName);
          }
          rowData[newHeaders.indexOf(headerName)] = answer.value;
        }
      });

      // Add Submitted At column
      if (!newHeaders.includes("Submitted At")) {
        newHeaders.push("Submitted At");
      }
      rowData[newHeaders.indexOf("Submitted At")] =
        body.record.submitted_at || new Date().toISOString();

      console.log("🆕 New Headers:", newHeaders);
      console.log("📝 Row Data:", rowData);

      // Update the sheet headers if there are any new ones
      if (newHeaders.length > existingHeaders.length) {
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: `${sheetName}!A1:Z1`,
          valueInputOption: "RAW",
          requestBody: {
            values: [newHeaders],
          },
        });
        console.log("✅ Updated Headers in Google Sheets");
      }

      // Append the new row of data
      await sheets.spreadsheets.values.append({
        spreadsheetId,
        range: `${sheetName}!A2:Z`,
        valueInputOption: "RAW",
        requestBody: {
          values: [rowData],
        },
      });
      console.log("✅ Data appended to Google Sheets");

      // If everything goes well, update webhook_execution_logs with a success message and status
      if (logEntry) {
        await supabase
          .from("automate_form_webhook_execution_logs")
          .update({
            response: "Successfully submitted to Google Sheets",
            status: "success",
          })
          .eq("id", logEntry.id);
      }

      // Send a success response back to the caller
      return sendApiResponse(c, {
        message: "Successfully submitted form response to Google Sheets",
        data: body,
      });
    }
    // Get valid Google access token for this credential
  } catch (err) {
    // Log the error in the database if a log entry was created
    console.error("Submit Form Data Error in GoogleSheet:", err);
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Error: ${err}`,
          status: "error",
        })
        .eq("id", logEntry.id);
    }

    // Return an error response
    return sendApiError(c, "Internal server error", 500);
  }
};


const sendEmailToRespondent = async (c: Context) => {
  let logEntry: { id: number } | null = null;

  try {
    // 1) Parse the request body
    const body = await c.req.json();

    if (!body?.record || !body.record.form_id || !body.record.answers) {
      throw new Error("Invalid request payload: Missing form_id or answers");
    }

    const { form_id, answers } = body.record;
    console.log("📧 Sending email to form respondent..., answers:", answers);

    // 2) Insert a row in webhook_execution_logs to track this request
    const { data: logData, error: logError } = await supabase
      .from("automate_form_webhook_execution_logs")
      .insert({
        response_id: body.record.id,
        data_sent: body.record,
        status: "pending",
      })
      .select("id")
      .single();

    if (logError) {
      console.error(
        "Failed to create webhook execution log:",
        logError.message
      );
    } else {
      logEntry = logData;
    }

    // 3) Fetch Gmail integration for the form
    const GMAIL_INTEGRATION_ID = "f5732279-8247-49a2-8453-501386423825"; // Replace with your actual Gmail integration ID
    const { data: formIntegrations, error: formIntegrationError } =
      await supabase
        .from("form_integrations")
        .select("credential_id, enabled, mapped_data, action_id")
        .eq("form_id", form_id)
        .eq("integration_id", GMAIL_INTEGRATION_ID)
        .single();
console.log("formIntegrations",formIntegrations)
    if (formIntegrationError || !formIntegrations) {
      console.error(
        "No Gmail integration found:",
        formIntegrationError?.message || "No integrations available"
      );
      return sendApiError(c, "Gmail integration not configured", 400);
    }

    // Check if the integration is enabled
    if (!formIntegrations.enabled) {
      console.error("Gmail integration is disabled");
      return sendApiError(c, "Gmail integration is disabled", 400);
    }

    const processed = processDataMapping(formIntegrations.mapped_data, answers);
    console.log("processed", processed);

    const recipientEmail = processed.to;
    const cc = processed.cc;
    const bcc = processed.bcc;
    const emailSubject = processed.subject;
    const emailBody = processed.body;

    const credentialId = formIntegrations.credential_id;
    const accessToken = await getValidGmailToken(credentialId);
    if (!accessToken) {
      console.error(
        `Failed to retrieve Gmail access token for credential: ${credentialId}`
      );
      return sendApiError(c, "Failed to authenticate with Gmail", 401);
    }

    // Get sender email from credentials
    const { data: credential } = await supabase
      .from("automate_form_integration_credentials")
      .select("auth_data")
      .eq("id", credentialId)
      .single();

    const senderEmail =
      credential?.auth_data?.email || "<EMAIL>";
    const fromAddress = credential?.auth_data?.email;

    // Get form title for the email
    const { data: form } = await supabase
      .from("automate_forms")
      .select("title")
      .eq("id", form_id)
      .single();

    const formTitle = form?.title || "Form Submission";
    const actionId = formIntegrations.action_id;

    // console.log(`📧 Processing Gmail Integration for: ${recipientEmail}`);
    // console.log(`🎯 Processing action: ${actionId}`);

    // Send email
    const success = await sendGmailMessage(
      accessToken,
      recipientEmail,
      fromAddress,
      emailSubject,
      emailBody,
      true, // isHtml
      cc,
      bcc
    );

    if (!success) {
      throw new Error("Failed to send email");
    }

    console.log("✅ Email sent successfully");

    // If everything goes well, update webhook_execution_logs with success
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Successfully sent email to ${recipientEmail}`,
          status: "success",
        })
        .eq("id", logEntry.id);
    }

    // Send a success response back to the caller
    return sendApiResponse(c, {
      message: "Successfully sent email to form respondent",
      data: { recipient: recipientEmail, subject: emailSubject },
    });
  } catch (err) {
    // Log the error in the database if a log entry was created
    console.error("Submit Form Data Error in Gmail:", err);
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Error: ${err}`,
          status: "error",
        })
        .eq("id", logEntry.id);
    }

    // Return an error response
    return sendApiError(c, "Internal server error", 500);
  }
};
const sendDataToSlack = async (c: Context) => {
  let logEntry: { id: number } | null = null;

  try {
    // 1. Parse and validate body
    const body = await c.req.json();
    const record = body?.record;

    if (!record?.form_id || !record?.answers) {
      console.error("❌ Missing form_id or answers in request body");
      return sendApiError(c, "Invalid request: Missing form_id or answers", 400);
    }

    const { form_id, answers } = record;

    // 2. Log webhook execution attempt
    const { data: logData, error: logError } = await supabase
      .from("automate_form_webhook_execution_logs")
      .insert({
        response_id: record.id,
        data_sent: record,
        status: "pending",
      })
      .select("id")
      .single();

    if (logError) {
      console.error("⚠️ Failed to create webhook log:", logError.message);
    } else {
      logEntry = logData;
    }

    // 3. Retrieve Slack integration details
    const SLACK_INTEGRATION_ID = "a5a5a4a9-e5ec-478c-bed8-58322b0d26cf";
    const { data: formIntegrations, error: formIntegrationError } = await supabase
      .from("form_integrations")
      .select("metadata, credential_id, enabled, mapped_data, action_id")
      .eq("form_id", form_id)
      .eq("integration_id", SLACK_INTEGRATION_ID)
      .single();

    if (formIntegrationError || !formIntegrations) {
      console.error("❌ Slack integration not found:", formIntegrationError?.message);
      return sendApiError(c, "Slack integration not configured", 400);
    }

    if (!formIntegrations.enabled) {
      console.warn("⚠️ Slack integration is disabled");
      return sendApiError(c, "Slack integration is disabled", 400);
    }

    const {
      metadata,
      credential_id: credentialId,
      mapped_data,
      action_id: actionId,
    } = formIntegrations;

    const accessToken = await getValidSlackToken(credentialId);
    if (!accessToken) {
      console.error("❌ Invalid Slack token for credential:", credentialId);
      return sendApiError(c, "Slack authentication failed", 401);
    }

    const channelId = metadata?.channel_id;
    const targetUserId = metadata?.target_user_id;
    const channelName = metadata?.channel_name;
    const targetChannel = actionId === "22bb3c03-e476-4b51-becb-837ea4853df6"
      ? targetUserId
      : channelId;

    if (!targetChannel) {
      console.error("❌ No target Slack channel or user ID configured");
      return sendApiError(c, "Slack target not configured", 400);
    }

    console.log(`📤 Sending Slack message to ${channelName || targetUserId}...`);

    // 4. Fetch form title
    const { data: form } = await supabase
      .from("automate_forms")
      .select("title")
      .eq("id", form_id)
      .single();

    const formTitle = form?.title || "Form Submission";
    const submittedAt = record.submitted_at || new Date().toISOString();

    // 5. Construct message
    let messageText = "";
    let messageBlocks: any = undefined;
        const processed = processDataMapping(mapped_data, answers);
        console.log("processed",processed)
         messageText = `📝 New form submission received for ${formTitle}`;
         messageBlocks = createSlackMessageBlocks(formTitle, processed, submittedAt);    // 6. Send to Slack
    try {
      let success;
      
      if (actionId === "17f2e4b4-a32c-4647-9288-cfd8415799ac") {
        // Send to user
        success = await sendSlackMessageToUser(
          accessToken,
          targetUserId,
          messageText,
          messageBlocks
        );
      } else {
        // Send to channel
        success = await sendSlackMessage(
          accessToken,
          targetChannel,
          messageText,
          messageBlocks
        );
      }

      if (!success) {
        throw new Error("Failed to send message to Slack");
      }

      console.log("✅ Slack message sent successfully");

      // Update log as successful
      if (logEntry) {
        await supabase
          .from("automate_form_webhook_execution_logs")
          .update({
            response: "Successfully sent to Slack",
            status: "success",
          })
          .eq("id", logEntry.id);
      }

      return sendApiResponse(c, {
        message: "Form response sent to Slack successfully",
        data: body,
      });

    } catch (slackError: any) {
      // Handle specific Slack errors
      if (slackError.message.includes("missing_scope")) {
        return sendApiError(
          c,
          "Slack bot needs additional permissions. Please reinstall the app.",
          403
        );
      } else if (slackError.message.includes("not_in_channel") || 
                 slackError.message.includes("Cannot access channel")) {
        return sendApiError(
          c,
          "Bot is not in the channel. Please invite the bot using /invite @YourBotName",
          403
        );
      }
      throw slackError; // Re-throw other errors
    }

  } catch (err: any) {
    console.error("❌ Submit Form Data Error:", err);

    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Error: ${err.message}`,
          status: "error",
        })
        .eq("id", logEntry.id);
    }

    // Return a more specific error message if available
    return sendApiError(c, err.message || "Internal server error", 500);
  }
};

const sendOutlookEmailToRespondent = async (c: Context) => {
  let logEntry: { id: number } | null = null;

  try {
    // 1) Parse the request body
    const body = await c.req.json();

    if (!body?.record || !body.record.form_id || !body.record.answers) {
      throw new Error("Invalid request payload: Missing form_id or answers");
    }

    const { form_id, answers } = body.record;
    console.log("📧 Sending email to form respondent..., answers:", answers);

    // 2) Insert a row in webhook_execution_logs to track this request
    const { data: logData, error: logError } = await supabase
      .from("automate_form_webhook_execution_logs")
      .insert({
        response_id: body.record.id,
        data_sent: body.record,
        status: "pending",
      })
      .select("id")
      .single();

    if (logError) {
      console.error(
        "Failed to create webhook execution log:",
        logError.message
      );
    } else {
      logEntry = logData;
    }

    // 3) Fetch Gmail integration for the form
    const OUTOOK_INTEGRATION_ID = "98f1dda7-4d14-4cfd-af79-64da3740d4e2"; // Replace with your actual Gmail integration ID
    const { data: formIntegrations, error: formIntegrationError } =
      await supabase
        .from("form_integrations")
        .select("credential_id, enabled, mapped_data, action_id")
        .eq("form_id", form_id)
        .eq("integration_id", OUTOOK_INTEGRATION_ID)
        .single();
console.log("formIntegrations",formIntegrations)
    if (formIntegrationError || !formIntegrations) {
      console.error(
        "No outllok integration found:",
        formIntegrationError?.message || "No integrations available"
      );
      return sendApiError(c, "outlook integration not configured", 400);
    }

    // Check if the integration is enabled
    if (!formIntegrations.enabled) {
      console.error("outllok integration is disabled");
      return sendApiError(c, "outlook integration is disabled", 400);
    }

    const processed = processDataMapping(formIntegrations.mapped_data, answers);
    console.log("processed", processed);

    const recipientEmail = processed.to;
    const cc = processed.cc;
    const bcc = processed.bcc;
    const emailSubject = processed.subject;
    const emailBody = processed.body;

    const credentialId = formIntegrations.credential_id;
    const accessToken = await getValidOutlookToken(credentialId);
    if (!accessToken) {
      console.error(
        `Failed to retrieve outlook access token for credential: ${credentialId}`
      );
      return sendApiError(c, "Failed to authenticate with Gmail", 401);
    }

    // Get sender email from credentials
    const { data: credential } = await supabase
      .from("automate_form_integration_credentials")
      .select("auth_data")
      .eq("id", credentialId)
      .single();

    const senderEmail =
      credential?.auth_data?.email || "<EMAIL>";
    const fromAddress = credential?.auth_data?.email;

    // Get form title for the email
    const { data: form } = await supabase
      .from("automate_forms")
      .select("title")
      .eq("id", form_id)
      .single();

    const formTitle = form?.title || "Form Submission";
    const actionId = formIntegrations.action_id;

    // console.log(`📧 Processing Gmail Integration for: ${recipientEmail}`);
    // console.log(`🎯 Processing action: ${actionId}`);

    // Send email
    const success = await sendOutlookMessage(
      accessToken,
      recipientEmail,
      fromAddress,
      emailSubject,
      emailBody,
      true, // isHtml
      cc,
      bcc
    );

    if (!success) {
      throw new Error("Failed to send email");
    }

    console.log("✅ Email sent successfully");

    // If everything goes well, update webhook_execution_logs with success
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Successfully sent email to ${recipientEmail}`,
          status: "success",
        })
        .eq("id", logEntry.id);
    }

    // Send a success response back to the caller
    return sendApiResponse(c, {
      message: "Successfully sent email to form respondent",
      data: { recipient: recipientEmail, subject: emailSubject },
    });
  } catch (err) {
    // Log the error in the database if a log entry was created
    console.error("Submit Form Data Error in Gmail:", err);
    if (logEntry) {
      await supabase
        .from("automate_form_webhook_execution_logs")
        .update({
          response: `Error: ${err}`,
          status: "error",
        })
        .eq("id", logEntry.id);
    }

    // Return an error response
    return sendApiError(c, "Internal server error", 500);
  }
};

export { sendDataToGoogleSheet, sendDataToSlack, sendEmailToRespondent, sendOutlookEmailToRespondent };
