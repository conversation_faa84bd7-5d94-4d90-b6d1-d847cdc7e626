import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
const updateFormSettings = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    const body = await c.req.json();
    const { is_public, accept_responses,copy_allowed } = body;

    if (is_public === undefined && accept_responses === undefined && copy_allowed===undefined) {
      return sendApiError(
        c,
        "At least one setting (is_public or accept_responses,copy_allowed) is required",
        400
      );
    }

    // Check if the form exists and belongs to the user
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "You are not authorized to update this form's settings",
        403
      );
    }

    // Update the form settings
    const updatedSettings: Record<string, any> = {};
    if (is_public !== undefined) updatedSettings.is_public = is_public;
    if (accept_responses !== undefined)
      updatedSettings.accept_responses = accept_responses;
    if(copy_allowed!==undefined)
        updatedSettings.copy_allowed = copy_allowed;

    const { error: updateError } = await supabase
      .from("automate_form_settings")
      .update(updatedSettings)
      .eq("form_id", formId);

    if (updateError) {
      console.error("Update Form Settings Error:", updateError);
      return sendApiError(c, "Failed to update form settings", 500);
    }

    return sendApiResponse(
      c,
      { message: "Form settings updated successfully" },
      200
    );
  } catch (err) {
    console.error("Update Form Settings Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const updateThankYouPageSettings = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    const body = await c.req.json();
    const { thank_you_type, thank_you_data, thank_you_url } = body;

    if (!thank_you_type) {
      return sendApiError(c, "Thank you type is required", 400);
    }

    if (thank_you_type === "custom" && !thank_you_data) {
      return sendApiError(c, "Custom thank you data is required", 400);
    }
    if (thank_you_type === "condition" && !thank_you_data) {
      return sendApiError(c, "Custom thank you data is required", 400);
    }

    if (thank_you_type === "redirect" && !thank_you_url) {
      return sendApiError(
        c,
        "Redirect URL is required for 'redirect' type",
        400
      );
    }

    // Validate thank_you_type using the ENUM values
    const validTypes = ["default", "custom", "redirect",'condition'];
    if (!validTypes.includes(thank_you_type)) {
      return sendApiError(c, "Invalid thank you type", 400);
    }

    // Check if the form exists and belongs to the user
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "You are not authorized to update this form's settings",
        403
      );
    }

    // Update the thank you page settings
    const updateData: Record<string, any> = {
      thank_you_type,
      thank_you_data: (thank_you_type === "custom" || thank_you_type === "condition") ? thank_you_data : null,
      thank_you_url: thank_you_type === "redirect" ? thank_you_url : null,
    };

    const { error: updateError } = await supabase
      .from("automate_form_settings")
      .update(updateData)
      .eq("form_id", formId);

    if (updateError) {
      console.error("Update Thank You Page Settings Error:", updateError);
      return sendApiError(c, "Failed to update thank you page settings", 500);
    }

    return sendApiResponse(
      c,
      { message: "Thank you page settings updated successfully" },
      200
    );
  } catch (err) {
    console.error("Update Thank You Page Settings Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getThankYouPageSettings = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Check if the form exists and belongs to the user
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "You are not authorized to access this form's settings",
        403
      );
    }

    // Fetch thank you page settings
    const { data: settings, error: settingsError } = await supabase
      .from("automate_form_settings")
      .select("thank_you_type, thank_you_data, thank_you_url")
      .eq("form_id", formId)
      .single();

    if (settingsError || !settings) {
      return sendApiError(c, "Thank you page settings not found", 404);
    }

    return sendApiResponse(
      c,
      {
        message: "Thank you page settings retrieved successfully",
        data: settings,
      },
      200
    );
  } catch (err) {
    console.error("Get Thank You Page Settings Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
export {
  updateFormSettings,
  updateThankYouPageSettings,
  getThankYouPageSettings,
};
