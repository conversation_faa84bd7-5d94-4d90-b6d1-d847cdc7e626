
import { Hono } from "hono";
import { cors } from "hono/cors";
import { serveStatic } from 'hono/bun'
import { user } from "./routes/user.routes";
import { form } from "./routes/form.routes";
import { field } from "./routes/field.routes";
import { submission } from "./routes/submission.routes";
import { response } from "./routes/response.routes";
import { googleAuth } from "./routes/goggleauth.routes";
import { webhook } from "./routes/webhook.routes";
import { integration } from "./routes/integration.routes";
import { googlesheet } from "./routes/googlesheetIntegration.routes";
import { workspace } from "./routes/workspace.routes";
import { whatsapp } from "./routes/whatsapp.routes";
import { slack } from "./routes/slack.routes";
import { gmail } from "./routes/gmail.routes";
import { outlook } from "./routes/outlook.routes";
import { folder } from "./routes/folder.routes";
import { formsetting } from "./routes/formsetting.routes";
import { formTemplate } from "./routes/formTemplate.routes";
import { formthemes } from "./routes/theme.routes";
import { crm } from "./routes/crm.routes";
import { apiLogger } from "./middleware/logging.middleware";
import { taskIntegration } from "./routes/taskIntegration.routes";
import { permission } from "./routes/permission.routes";
import { role } from "./routes/role.routes";
import {  billingRoutes } from "./routes/billing.routes";
import { formWebhook } from "./routes/formWebhook.routes";
import { subscriptionRoutes } from "./routes/subscription.routes";
import { formShare } from "./routes/formShare.routes";
import { usage } from "./routes/usage.routes";
import { supportRoutes } from "./routes/support.routes";
import { analyticsRouter } from "./routes/analytics.routes";
import { tutorial } from "./routes/tutorials.routes";
import {filter}  from "./routes/responseFilterView.routes"
import { morganLogger } from "./middleware/logger";
//server
const app = new Hono();
//cors handling
const envOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(",")
  : [];
  const allowedOrigins = new Set(envOrigins);
app.use(
  cors({
    origin: (origin) => {
      if (!origin) return "http://localhost:3000";
      return allowedOrigins.has(origin) ? origin : null;
    },
    credentials: true,
  })
);
// Add logging middleware AFTER cors but BEFORE routes
// app.use("*", apiLogger);
app.use("*", morganLogger());
//static files
app.use("/public/*", serveStatic({ root: "./public" }));

//request size limit
app.use("*", async (c, next) => {
  const contentType = c.req.header("Content-Type") || "";
  console.log(`📢 Incoming Request: ${c.req.method} ${c.req.url}`);
  if (
    contentType.includes("application/json") ||
    contentType.includes("application/x-www-form-urlencoded")
  ) {
    const contentLength = c.req.header("Content-Length");
    if (contentLength && parseInt(contentLength) > 5 * 1024 * 1024) {
      return c.text("Payload Too Large", 413);
    }
  }
  await next();
});

//routes
app.route("/v1/users", user);
app.route("/v1/forms", form);
app.route("/v1/formsetting", formsetting);
app.route("/v1/forms/submission", submission);
app.route("/v1/fields", field);
app.route("/v1/response", response);
app.route("/auth", googleAuth);
app.route("/v1/webhook", webhook);
app.route("/v1/integration", integration);
app.route("/v1/google/sheet", googlesheet);
app.route("/v1/workspace", workspace);
app.route("/v1/whatsapp", whatsapp);
app.route("/v1/slack", slack);
app.route("/v1/gmail", gmail);
app.route("/v1/outlook", outlook);
app.route("/v1/folder", folder);
app.route("/v1/form/template", formTemplate);
app.route("/v1/themes", formthemes);
app.route("/v1/crm", crm);
app.route("/v1/task", taskIntegration);
app.route("/v1/permissions", permission);
app.route("/v1/roles", role);
app.route("/v1/wallet", billingRoutes);
app.route("/v1/form/webhook", formWebhook);
app.route("/v1/subscription", subscriptionRoutes);
app.route('/v1/form-share', formShare);
app.route('/v1/usage', usage);
app.route('/v1/support', supportRoutes);
app.route("/v1/analytics",analyticsRouter)
app.route("/v1/tutorial",tutorial)
app.route("/v1/filterview",filter)
//default route
app.get("/", (c) => c.text("API is working!"));
//404
app.notFound((c) => {
  return c.text("Api enpoint not found", 404);
});

export {app}
